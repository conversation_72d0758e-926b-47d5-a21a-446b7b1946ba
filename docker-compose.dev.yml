version: '3.8'

services:
  chatppt-mcp-server-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: chatppt-mcp-server-dev
    restart: unless-stopped
    ports:
      - "${MCP_PORT:-8000}:${MCP_PORT:-8000}"
    environment:
      # Required: Your ChatPPT API Key from www.yoo-ai.com
      - API_PPT_KEY=${API_PPT_KEY}
      
      # Development settings
      - LOG_LEVEL=DEBUG
      - MCP_PORT=${MCP_PORT:-8000}
      - API_BASE=${API_BASE:-https://saas.api.yoo-ai.com}
      - MCP_TRANSPORT=${MCP_TRANSPORT:-sse}
      
      # Optional: SSE Access Key (disabled for development)
      # - SSE_ACCESS_KEY=${SSE_ACCESS_KEY}
    volumes:
      # Mount source code for development
      - .:/app
      - /app/vendor
    working_dir: /app
    command: ["go", "run", ".", "-t", "${MCP_TRANSPORT:-sse}"]
    networks:
      - chatppt-dev-network
    labels:
      - "com.chatppt.service=mcp-server-dev"
      - "com.chatppt.environment=development"

networks:
  chatppt-dev-network:
    driver: bridge
    name: chatppt-dev-network
