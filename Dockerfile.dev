# Development Dockerfile
FROM golang:1.21-alpine

# Install development tools
RUN apk add --no-cache git ca-certificates wget curl

# Install air for hot reloading (optional)
RUN go install github.com/cosmtrek/air@latest

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Expose port
EXPOSE 8000

# Default command for development
CMD ["go", "run", ".", "-t", "sse"]
