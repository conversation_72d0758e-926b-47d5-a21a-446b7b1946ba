# 任务绑定功能 (Task Binding Feature)

## 概述

新增的任务绑定功能解决了MCP调用时AI上下文传递的问题。现在生成任务时返回的project_id会自动与dstaff的task_id绑定，后续操作只需要传递task_id即可自动获取对应的project_id。

## 功能特性

### 1. 自动绑定机制
- 当调用生成工具（如 `build_ppt`, `text_build_ppt`, `build_ppt_by_file`, `build_thesis_ppt`）时
- 系统会自动提取返回结果中的project_id（ppt_id）
- 将task_id与project_id的绑定关系保存到本地存储

### 2. 智能参数解析
- 查询和操作工具（如 `query_ppt`, `download_ppt`, `editor_ppt`等）现在支持两种方式：
  - **直接传递ppt_id**：保持向后兼容性
  - **通过task_id自动获取**：如果未提供ppt_id，系统会从task_id获取绑定的project_id

### 3. 持久化存储
- 绑定关系保存在 `./data/task_bindings.json` 文件中
- 支持服务重启后数据恢复
- 提供统计信息和管理功能

## 使用方法

### 生成PPT（自动创建绑定）
```json
{
  "tool": "build_ppt",
  "arguments": {
    "theme": "AI技术介绍",
    "Context": {
      "task_id": "task-123"
    }
  }
}
```
系统会自动保存 `task-123` 与返回的 `ppt_id` 的绑定关系。

### 查询PPT（使用绑定）
```json
{
  "tool": "query_ppt",
  "arguments": {
    "Context": {
      "task_id": "task-123"
    }
  }
}
```
系统会自动从 `task-123` 获取绑定的 `ppt_id` 进行查询。

### 向后兼容（直接传递ppt_id）
```json
{
  "tool": "query_ppt",
  "arguments": {
    "ppt_id": "ppt-456",
    "Context": {
      "task_id": "task-123"
    }
  }
}
```
如果直接提供了 `ppt_id`，系统会优先使用提供的值。

## 支持的工具

### 生成工具（自动创建绑定）
- `build_ppt` - 主题生成PPT
- `text_build_ppt` - 文本生成PPT
- `build_ppt_by_file` - 文件生成PPT
- `build_thesis_ppt` - 论文生成答辩PPT

### 查询和操作工具（支持绑定解析）
- `query_ppt` - 查询PPT生成进度
- `download_ppt` - 下载PPT文件
- `editor_ppt` - 生成编辑器地址
- `replace_ppt` - 替换PPT内容
- `set_font_ppt` - 设置PPT字体
- `set_anim_ppt` - 设置PPT动画
- `ppt_create_note` - 生成演讲稿
- `ppt_add_slides` - 添加幻灯片
- `ppt_replace_user_select_template` - 替换模板

## 技术实现

### 核心组件
1. **TaskBindingManager** (`internal/binding/task_binding.go`)
   - 管理task_id和project_id的绑定关系
   - 提供创建、查询、更新、删除功能
   - 支持持久化存储和统计信息

2. **工具处理器增强** (`internal/tools/handlers.go`)
   - `extractProjectIDFromResult()` - 从API结果提取project_id
   - `resolveProjectID()` - 智能解析project_id（优先使用直接传递的值，否则从绑定获取）
   - 生成工具自动保存绑定关系
   - 查询工具自动解析绑定关系

3. **工具定义更新** (`internal/tools/definitions.go`)
   - ppt_id参数从必需改为可选
   - 更新工具描述，说明支持绑定机制

### 数据结构
```go
type TaskBinding struct {
    TaskID    string    `json:"task_id"`
    ProjectID string    `json:"project_id"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 存储格式
```json
{
  "task-123": {
    "task_id": "task-123",
    "project_id": "ppt-456",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

## 优势

1. **简化调用**：后续操作只需传递task_id，无需记住project_id
2. **避免上下文丢失**：解决AI上下文传递问题
3. **向后兼容**：现有代码无需修改，仍可直接传递ppt_id
4. **自动管理**：绑定关系自动创建和维护
5. **持久化**：服务重启后绑定关系不丢失

## 注意事项

1. 确保 `./data` 目录有写入权限
2. task_id应该在整个会话中保持唯一
3. 如果同时提供ppt_id和task_id，优先使用ppt_id
4. 绑定关系会在生成工具成功返回后自动创建
5. 定期清理过期的绑定关系以节省存储空间
