package tools

import "github.com/mark3labs/mcp-go/mcp"

// GetAllTools returns all available tools for the ChatPPT MCP server
func GetAllTools() []mcp.Tool {
	return []mcp.Tool{
		// PPT Query Tool
		mcp.NewTool("query_ppt",
			mcp.WithDescription("查询PPT生成进度。根据PPT任务ID查询异步生成结果，status=1表示还在生成中，应该继续轮训该查询，status=2表示成功，status=3表示失败；process_url表示预览的url地址，不断轮训请求直至成功或失败;当成功后使用默认浏览器打开ppt地址并调用download_ppt工具下载PPT和工具editor_ppt生成编辑器地址；可以传入ppt_id或通过task_id自动获取绑定的project_id；"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，当ppt_id未提供时用于获取绑定的project_id",
					},
				}),
			),
		),

		// PPT Build Tool
		mcp.NewTool("build_ppt",
			mcp.WithDescription("PPT生成。当用户需要生成PPT时，调用此工具。根据主题生成ppt。当返回PPT-ID时，表示生成任务成功，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("theme",
				mcp.Required(),
				mcp.Description("输入描述的描述生成主题或markdown，生成PPT"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Text Build PPT Tool
		mcp.NewTool("text_build_ppt",
			mcp.WithDescription("根据长文本需求（50字以上，不是ID，这个不是查询接口）生成PPT。当返回PPT-ID时，表示生成任务成功，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("text",
				mcp.Required(),
				mcp.Description("输入描述的文本（50字以上）或markdown，生成PPT"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Build PPT by File Tool
		mcp.NewTool("build_ppt_by_file",
			mcp.WithDescription("文件生成PPT。根据用户上传的文件（给出文件url地址），执行生成PPT的任务。当返回PPT-ID时，表示生成任务成功，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("用户给定的文件url地址，可以支持包括MarkDown、word、PDF、XMind、FreeMind、TXT 等文档文件"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Build Thesis PPT Tool
		mcp.NewTool("build_thesis_ppt",
			mcp.WithDescription("论文文件生成答辩PPT。根据用户上传的文件（给出文件url地址），执行生成PPT的任务。当返回PPT-ID时，表示生成任务成功，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("file_url",
				mcp.Required(),
				mcp.Description("用户的论文文件地址url，仅支持pdf、word、pdf三种文件"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Download PPT Tool
		mcp.NewTool("download_ppt",
			mcp.WithDescription("下载PPT。根据PPT任务ID下载生成的PPT文件，可以传入ppt_id或通过task_id自动获取绑定的project_id"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，当ppt_id未提供时用于获取绑定的project_id",
					},
				}),
			),
		),

		// Editor PPT Tool
		mcp.NewTool("editor_ppt",
			mcp.WithDescription("生成PPT编辑器地址。根据PPT任务ID生成在线编辑器链接，用户可以在线编辑PPT，可以传入ppt_id或通过task_id自动获取绑定的project_id"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，当ppt_id未提供时用于获取绑定的project_id",
					},
				}),
			),
		),

		// Replace PPT Tool
		mcp.NewTool("replace_ppt",
			mcp.WithDescription("替换PPT内容。根据PPT任务ID和新的内容替换现有PPT，可以传入ppt_id或通过task_id自动获取绑定的project_id"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithString("new_content",
				mcp.Required(),
				mcp.Description("新的PPT内容"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，当ppt_id未提供时用于获取绑定的project_id",
					},
				}),
			),
		),

		// Set Font PPT Tool
		mcp.NewTool("set_font_ppt",
			mcp.WithDescription("设置PPT字体。根据PPT任务ID设置PPT的字体样式"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithString("font_name",
				mcp.Required(),
				mcp.Description("字体名称"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，当ppt_id未提供时用于获取绑定的project_id",
					},
				}),
			),
		),

		// Set Animation PPT Tool
		mcp.NewTool("set_anim_ppt",
			mcp.WithDescription("设置PPT动画。根据PPT任务ID设置PPT的动画效果"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithString("set_anim",
				mcp.Description("是否设置动画，默认为'1'表示设置动画，'0'表示取消动画"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Create Note PPT Tool
		mcp.NewTool("ppt_create_note",
			mcp.WithDescription("生成演讲稿。参照给出的任务PPT-ID自动为用户的ppt生成全文演讲稿，并会返回新的PPT-ID，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Add Slides PPT Tool
		mcp.NewTool("ppt_add_slides",
			mcp.WithDescription("添加PPT幻灯片。向现有PPT添加新的幻灯片"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithString("slide_text",
				mcp.Required(),
				mcp.Description("幻灯片内容文本"),
			),
			mcp.WithString("slide_type",
				mcp.Description("幻灯片类型，默认为'内容页'"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Create Outline PPT Tool
		mcp.NewTool("ppt_create_outline_text",
			mcp.WithDescription("根据用户语输入的主题文本生成大纲内容。根据用户输入的内容ppt_text，实时生成大纲内容，直接返回大纲文本内容。"),
			mcp.WithString("ppt_text",
				mcp.Required(),
				mcp.Description("PPT-themeText，用户输入的文本，必须参数。"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Create Template Cover Tool
		mcp.NewTool("ppt_create_template_cover_image",
			mcp.WithDescription("生成PPT模板封面。根据主题文本生成多个模板封面供用户选择"),
			mcp.WithString("ppt_text",
				mcp.Required(),
				mcp.Description("PPT主题文本"),
			),
			mcp.WithString("ppt_color",
				mcp.Description("Template-color，指定生成模板的颜色，可以为空，表示随机；也可以从\"紫色\",\"红色\",\"橙色\",\"黄色\",\"绿色\",\"青色\",\"蓝色\",\"粉色\",\"灰色\"进行指定，可选参数。"),
			),
			mcp.WithString("ppt_style",
				mcp.Description("Template-style，指定生成模板的风格，默认为'科技风'"),
			),
			mcp.WithString("ppt_num",
				mcp.Description("Template-num，指定生成模板数量，默认为4"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Replace Template Cover Tool
		mcp.NewTool("ppt_replace_user_select_template",
			mcp.WithDescription("通过Cover-ID替换/更换指定模板。根据工具Cover-ID和任务PPT-ID执行替换为用户指定（根据cover_id）的模板，并返回新的任务PPT-ID，可以调用query_ppt工具查询生成进度和预览URL"),
			mcp.WithString("ppt_id",
				mcp.Description("PPT-ID，可选参数。如果不提供，将从task_id获取绑定的project_id"),
			),
			mcp.WithString("cover_id",
				mcp.Required(),
				mcp.Description("cover_id，用户指定的模板id，需要通过ppt_create_template_coverImage 进行生成。"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID",
					},
				}),
			),
		),

		// Complete PPT Generation Tool
		mcp.NewTool("generate_ppt_complete",
			mcp.WithDescription("完整的PPT生成流程工具。接收PPT生成需求，自动执行生成->监测状态->下载->上传(DStaff)的完整流程，并通过进度报告实时反馈每个步骤的执行状态。支持主题生成、文本生成、文件生成和论文答辩PPT生成。"),
			mcp.WithString("theme",
				mcp.Description("PPT主题，用于主题生成模式"),
			),
			mcp.WithString("text",
				mcp.Description("PPT文本内容，用于文本生成模式（50字以上）"),
			),
			mcp.WithString("file_url",
				mcp.Description("文件URL，用于文件生成模式，支持MarkDown、Word、PDF、XMind、FreeMind、TXT等格式"),
			),
			mcp.WithBoolean("is_thesis",
				mcp.Description("是否为论文答辩PPT，当file_url提供时有效，默认为false"),
			),
			mcp.WithObject("Context",
				mcp.Description("上下文参数，包含task_id等信息"),
				mcp.Properties(map[string]any{
					"task_id": map[string]any{
						"type":        "string",
						"description": "任务ID，用于进度跟踪和DStaff集成",
					},
				}),
			),
		),

	}
}
