package tools

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"chatppt-mcp-go/internal/auth"
	"chatppt-mcp-go/internal/binding"
	"chatppt-mcp-go/internal/config"
	"chatppt-mcp-go/internal/progress"
	"github.com/mark3labs/mcp-go/mcp"
)

// HTTPClient interface for making HTTP requests (for testing)
type HTTPClient interface {
	Do(req *http.Request) (*http.Response, error)
}

// ToolHandler handles tool execution
type ToolHandler struct {
	client           HTTPClient
	apiBase          string
	dstaffConfig     *config.DStaffConfig
	bindingManager   *binding.TaskBindingManager
	progressSendFunc func(notification *mcp.ProgressNotification) error
	logSendFunc      func(notification *mcp.LoggingMessageNotification) error
}

// NewToolHandler creates a new tool handler
func NewToolHandler(apiBase string, dstaffConfig *config.DStaffConfig, bindingManager *binding.TaskBindingManager) *ToolHandler {
	return &ToolHandler{
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
		apiBase:        apiBase,
		dstaffConfig:   dstaffConfig,
		bindingManager: bindingManager,
	}
}

// SetProgressSendFunc sets the function to send progress notifications
func (h *ToolHandler) SetProgressSendFunc(sendFunc func(notification *mcp.ProgressNotification) error) {
	h.progressSendFunc = sendFunc
}

// SetLogSendFunc sets the function to send log notifications
func (h *ToolHandler) SetLogSendFunc(sendFunc func(notification *mcp.LoggingMessageNotification) error) {
	h.logSendFunc = sendFunc
}

// SetProgressFunctions sets both progress and log send functions
func (h *ToolHandler) SetProgressFunctions(progressSendFunc func(notification *mcp.ProgressNotification) error, logSendFunc func(notification *mcp.LoggingMessageNotification) error) {
	h.progressSendFunc = progressSendFunc
	h.logSendFunc = logSendFunc
}

// HandleToolCall handles all tool calls with detailed logging like the reference implementation
func (h *ToolHandler) HandleToolCall(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	toolName := request.Params.Name
	arguments := request.Params.Arguments

	// Log detailed request information (similar to ref implementation)
	log.Printf("🔧 Tool call received: %s", toolName)
	log.Printf("📝 Arguments: %+v", arguments)

	// Output detailed request body information for debugging
	requestJSON, _ := json.MarshalIndent(request, "", "  ")
	log.Printf("=== MCP Tool Call: %s ===", toolName)
	log.Printf("Request Body: %s", string(requestJSON))
	log.Printf("=======================================")

	// Convert arguments to map[string]interface{}
	argsMap, ok := arguments.(map[string]interface{})
	if !ok {
		errorResult := &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Invalid arguments type: expected map[string]interface{}, got %T", arguments),
				},
			},
			IsError: true,
		}

		// Log error response
		resultJSON, _ := json.MarshalIndent(errorResult, "", "  ")
		log.Printf("=== MCP Tool Error Response: %s ===", toolName)
		log.Printf("Error Response Body: %s", string(resultJSON))
		log.Printf("=====================================")

		return errorResult, nil
	}

	// Extract DStaff authentication context if available
	var dstaffAuth *auth.DStaffAuthContext
	if token := auth.ExtractTokenFromContext(ctx); token != "" {
		// Extract task_id from request arguments
		taskID := h.extractTaskIDFromRequest(argsMap)
		dstaffAuth = &auth.DStaffAuthContext{
			Token:  token,
			TaskID: taskID,
		}
		log.Printf("🔐 DStaff auth context: task_id=%s, token_length=%d", taskID, len(token))
	}

	// Route to appropriate handler based on tool name
	var result *mcp.CallToolResult
	var err error

	switch toolName {
	case "query_ppt":
		result, err = h.handleQueryPPT(ctx, argsMap)
	case "build_ppt":
		result, err = h.handleBuildPPT(ctx, argsMap)
	case "text_build_ppt":
		result, err = h.handleTextBuildPPT(ctx, argsMap)
	case "build_ppt_by_file":
		result, err = h.handleBuildPPTByFile(ctx, argsMap)
	case "build_thesis_ppt":
		result, err = h.handleBuildThesisPPT(ctx, argsMap)
	case "download_ppt":
		result, err = h.handleDownloadPPTWithDStaff(ctx, argsMap, dstaffAuth)
	case "editor_ppt":
		result, err = h.handleEditorPPT(ctx, argsMap)
	case "replace_ppt":
		result, err = h.handleReplacePPT(ctx, argsMap)
	case "set_font_ppt":
		result, err = h.handleSetFontPPT(ctx, argsMap)
	case "set_anim_ppt":
		result, err = h.handleSetAnimPPT(ctx, argsMap)
	case "ppt_create_note":
		result, err = h.handlePPTCreateNote(ctx, argsMap)
	case "ppt_add_slides":
		result, err = h.handlePPTAddSlides(ctx, argsMap)
	case "ppt_create_outline_text":
		result, err = h.handlePPTCreateOutlineText(ctx, argsMap)
	case "ppt_create_template_cover_image":
		result, err = h.handlePPTCreateTemplateCoverImage(ctx, argsMap)
	case "ppt_replace_user_select_template":
		result, err = h.handlePPTReplaceUserSelectTemplate(ctx, argsMap)
	case "generate_ppt_complete":
		result, err = h.handleGeneratePPTComplete(ctx, request, argsMap, dstaffAuth)
	default:
		log.Printf("❌ Unknown tool requested: %s", toolName)
		result = &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Unknown tool: %s", toolName),
				},
			},
			IsError: true,
		}
	}

	// Handle errors by converting them to error results
	if err != nil {
		log.Printf("❌ Tool execution error for %s: %v", toolName, err)
		result = &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Tool execution failed: %v", err),
				},
			},
			IsError: true,
		}
	}

	// Log the response for debugging (similar to ref implementation)
	if result != nil {
		resultJSON, _ := json.MarshalIndent(result, "", "  ")
		if result.IsError {
			log.Printf("=== MCP Tool Error Response: %s ===", toolName)
		} else {
			log.Printf("=== MCP Tool Response: %s ===", toolName)
		}
		log.Printf("Response Body: %s", string(resultJSON))
		log.Printf("=====================================")
	}

	return result, nil
}

// extractTaskIDFromRequest extracts task_id from request arguments (similar to ref implementation)
func (h *ToolHandler) extractTaskIDFromRequest(argsMap map[string]interface{}) string {
	// Try to extract from Context parameter
	if contextParam, ok := argsMap["Context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract from context parameter (lowercase)
	if contextParam, ok := argsMap["context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract directly from arguments
	if taskID, ok := argsMap["task_id"].(string); ok {
		return taskID
	}

	return ""
}

// handleQueryPPT handles the query_ppt tool
func (h *ToolHandler) handleQueryPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("🔍 Executing 'query_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	params := url.Values{}
	params.Set("id", pptID)

	return h.makeGetRequest(ctx, "/mcp/ppt/ppt-result", params, apiKey)
}

// handleBuildPPT handles the build_ppt tool
func (h *ToolHandler) handleBuildPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	theme, ok := arguments["theme"].(string)
	if !ok || theme == "" {
		return nil, fmt.Errorf("theme is required and must be a string")
	}

	log.Printf("🏗️ Executing 'build_ppt' tool with theme: %s", theme)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Prepare form data
	data := url.Values{}
	data.Set("text", theme)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleTextBuildPPT handles the text_build_ppt tool
func (h *ToolHandler) handleTextBuildPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	text, ok := arguments["text"].(string)
	if !ok || text == "" {
		return nil, fmt.Errorf("text is required and must be a string")
	}

	log.Printf("📄 Executing 'text_build_ppt' tool with text length: %d characters", len(text))

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Prepare form data
	data := url.Values{}
	data.Set("text", text)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// makeGetRequest is a helper function for making GET requests
func (h *ToolHandler) makeGetRequest(ctx context.Context, endpoint string, params url.Values, apiKey string) (*mcp.CallToolResult, error) {
	reqURL := h.apiBase + endpoint
	if len(params) > 0 {
		reqURL += "?" + params.Encode()
	}

	log.Printf("🌐 Making GET request to: %s", reqURL)

	req, err := http.NewRequestWithContext(ctx, "GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", auth.CreateAuthHeader(apiKey))
	req.Header.Set("User-Agent", "ChatPPT-MCP-Go/1.0")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("📊 Response status: %d", resp.StatusCode)
	log.Printf("📝 Response body: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	log.Printf("✅ API request completed successfully")
	return mcp.NewToolResultText(string(body)), nil
}

// makePostRequest is a helper function for making POST requests
func (h *ToolHandler) makePostRequest(ctx context.Context, endpoint string, data url.Values, apiKey string) (*mcp.CallToolResult, error) {
	reqURL := h.apiBase + endpoint
	log.Printf("🌐 Making API request to: %s", reqURL)

	req, err := http.NewRequestWithContext(ctx, "POST", reqURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Authorization", auth.CreateAuthHeader(apiKey))

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("📡 API response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("✅ API request completed successfully")
	return mcp.NewToolResultText(string(body)), nil
}

// handleBuildPPTByFile handles the build_ppt_by_file tool
func (h *ToolHandler) handleBuildPPTByFile(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	fileURL, ok := arguments["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url is required and must be a string")
	}

	log.Printf("📁 Executing 'build_ppt_by_file' tool with file URL: %s", fileURL)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("file_url", fileURL)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create-file", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleBuildThesisPPT handles the build_thesis_ppt tool
func (h *ToolHandler) handleBuildThesisPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	fileURL, ok := arguments["file_url"].(string)
	if !ok || fileURL == "" {
		return nil, fmt.Errorf("file_url is required and must be a string")
	}

	log.Printf("🎓 Executing 'build_thesis_ppt' tool with file URL: %s", fileURL)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("file_key", fileURL)

	result, err := h.makePostRequest(ctx, "/mcp/ppt/ppt-create-thesis", data, apiKey)
	if err != nil {
		return result, err
	}

	// Extract task_id and save binding if both are available
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID != "" && result != nil && !result.IsError {
		if projectID := h.extractProjectIDFromResult(result); projectID != "" {
			if bindErr := h.bindingManager.BindTaskToProject(taskID, projectID); bindErr != nil {
				log.Printf("⚠️ Failed to save task binding (task_id=%s, project_id=%s): %v", taskID, projectID, bindErr)
				// Don't fail the entire operation, just log the error
			}
		}
	}

	return result, err
}

// handleDownloadPPT handles the download_ppt tool
func (h *ToolHandler) handleDownloadPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("⬇️ Executing 'download_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	params := url.Values{}
	params.Set("id", pptID)

	// Get download URL from API
	result, err := h.makeGetRequest(ctx, "/mcp/ppt/ppt-download", params, apiKey)
	if err != nil {
		return result, err
	}

	// If DStaff is enabled, try to upload the file to DStaff platform
	if h.dstaffConfig.Enabled {
		log.Printf("🔗 DStaff enabled, attempting to upload PPT file to DStaff platform")

		// Get token from context and task_id from arguments
		token := auth.ExtractTokenFromContext(ctx)
		taskID := h.extractTaskIDFromRequest(arguments)

		if token != "" && taskID != "" {
			// Try to extract download URL from the result
			authCtx := &auth.DStaffAuthContext{
				Token:  token,
				TaskID: taskID,
			}
			if err := h.uploadPPTToDStaff(ctx, pptID, result, authCtx); err != nil {
				log.Printf("⚠️ Failed to upload PPT to DStaff: %v", err)
				// Don't fail the entire operation, just log the error
			}
		} else {
			log.Printf("⚠️ DStaff auth context not available (token: %s, task_id: %s), skipping upload",
				func() string {
					if token != "" {
						return "present"
					} else {
						return "missing"
					}
				}(),
				func() string {
					if taskID != "" {
						return "present"
					} else {
						return "missing"
					}
				}())
		}
	}

	return result, nil
}

// handleDownloadPPTWithDStaff handles the download_ppt tool with DStaff integration
func (h *ToolHandler) handleDownloadPPTWithDStaff(ctx context.Context, arguments map[string]interface{}, dstaffAuth *auth.DStaffAuthContext) (*mcp.CallToolResult, error) {
	pptID, ok := arguments["ppt_id"].(string)
	if !ok || pptID == "" {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: "ppt_id is required and must be a string",
				},
			},
			IsError: true,
		}, nil
	}

	log.Printf("⬇️ Executing 'download_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get API key: %v", err),
				},
			},
			IsError: true,
		}, nil
	}

	params := url.Values{}
	params.Set("id", pptID)

	// Get download URL from API
	result, err := h.makeGetRequest(ctx, "/mcp/ppt/ppt-download", params, apiKey)
	if err != nil {
		return &mcp.CallToolResult{
			Content: []mcp.Content{
				mcp.TextContent{
					Type: "text",
					Text: fmt.Sprintf("Failed to get download URL: %v", err),
				},
			},
			IsError: true,
		}, nil
	}

	// If DStaff is enabled and auth context is available, upload the file to DStaff platform
	if h.dstaffConfig.Enabled && dstaffAuth != nil && dstaffAuth.Token != "" && dstaffAuth.TaskID != "" {
		log.Printf("🔗 DStaff enabled, attempting to upload PPT file to DStaff platform")

		fileSize, err := h.uploadPPTToDStaffWithSize(ctx, pptID, result, dstaffAuth)
		if err != nil {
			log.Printf("⚠️ Failed to upload PPT to DStaff: %v", err)
			// Don't fail the entire operation, just add error info to the response
			return h.createDStaffUploadResponseWithSize(result, pptID, dstaffAuth, false, err.Error(), 0), nil
		} else {
			log.Printf("✅ Successfully uploaded PPT to DStaff platform")
			// Create success response with attachment info
			return h.createDStaffUploadResponseWithSize(result, pptID, dstaffAuth, true, "", fileSize), nil
		}
	} else {
		log.Printf("⚠️ DStaff upload skipped - either disabled or missing auth context")
	}

	return result, nil
}

// handleEditorPPT handles the editor_ppt tool
func (h *ToolHandler) handleEditorPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("✏️ Executing 'editor_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-editor", data, apiKey)
}

// handleReplacePPT handles the replace_ppt tool
func (h *ToolHandler) handleReplacePPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	newContent, ok := arguments["new_content"].(string)
	if !ok || newContent == "" {
		return nil, fmt.Errorf("new_content is required and must be a string")
	}

	log.Printf("🔄 Executing 'replace_ppt' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("content", newContent)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-replace", data, apiKey)
}

// handleSetFontPPT handles the set_font_ppt tool
func (h *ToolHandler) handleSetFontPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	fontName, ok := arguments["font_name"].(string)
	if !ok || fontName == "" {
		return nil, fmt.Errorf("font_name is required and must be a string")
	}

	log.Printf("🔤 Executing 'set_font_ppt' tool for PPT ID: %s, font: %s", pptID, fontName)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("font_name", fontName)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handleSetAnimPPT handles the set_anim_ppt tool
func (h *ToolHandler) handleSetAnimPPT(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	setAnim, ok := arguments["set_anim"].(string)
	if !ok || setAnim == "" {
		setAnim = "1" // Default to enable animation
	}

	log.Printf("🎬 Executing 'set_anim_ppt' tool for PPT ID: %s, set_anim: %s", pptID, setAnim)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("transition", setAnim)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handlePPTCreateNote handles the ppt_create_note tool
func (h *ToolHandler) handlePPTCreateNote(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	log.Printf("📝 Executing 'ppt_create_note' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("note", "1")

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// handlePPTAddSlides handles the ppt_add_slides tool
func (h *ToolHandler) handlePPTAddSlides(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	slideText, ok := arguments["slide_text"].(string)
	if !ok || slideText == "" {
		return nil, fmt.Errorf("slide_text is required and must be a string")
	}

	slideType, _ := arguments["slide_type"].(string)
	if slideType == "" {
		slideType = "内容页"
	}

	log.Printf("➕ Executing 'ppt_add_slides' tool for PPT ID: %s", pptID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("slide_text", slideText)
	data.Set("slide_type", slideType)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-page", data, apiKey)
}

// handlePPTCreateOutlineText handles the ppt_create_outline_text tool
func (h *ToolHandler) handlePPTCreateOutlineText(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	pptText, ok := arguments["ppt_text"].(string)
	if !ok || pptText == "" {
		return nil, fmt.Errorf("ppt_text is required and must be a string")
	}

	log.Printf("📋 Executing 'ppt_create_outline_text' tool")

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("text", pptText)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-structure", data, apiKey)
}

// handlePPTCreateTemplateCoverImage handles the ppt_create_template_cover_image tool
func (h *ToolHandler) handlePPTCreateTemplateCoverImage(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	pptText, ok := arguments["ppt_text"].(string)
	if !ok || pptText == "" {
		return nil, fmt.Errorf("ppt_text is required and must be a string")
	}

	log.Printf("🎨 Executing 'ppt_create_template_cover_image' tool")

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("title", pptText)

	// Handle optional parameters
	if pptColor, ok := arguments["ppt_color"].(string); ok && pptColor != "" {
		data.Set("color", pptColor)
	}

	if pptStyle, ok := arguments["ppt_style"].(string); ok && pptStyle != "" {
		data.Set("style", pptStyle)
	} else {
		data.Set("style", "科技风")
	}

	if pptNum, ok := arguments["ppt_num"].(float64); ok {
		data.Set("count", strconv.Itoa(int(pptNum)))
	} else {
		data.Set("count", "4")
	}

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-cover", data, apiKey)
}

// handlePPTReplaceUserSelectTemplate handles the ppt_replace_user_select_template tool
func (h *ToolHandler) handlePPTReplaceUserSelectTemplate(ctx context.Context, arguments map[string]interface{}) (*mcp.CallToolResult, error) {
	// Try to resolve project_id from arguments or task_id binding
	pptID, err := h.resolveProjectID(arguments)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve ppt_id: %w", err)
	}

	coverID, ok := arguments["cover_id"].(string)
	if !ok || coverID == "" {
		return nil, fmt.Errorf("cover_id is required and must be a string")
	}

	log.Printf("🔄 Executing 'ppt_replace_user_select_template' tool for PPT ID: %s, Cover ID: %s", pptID, coverID)

	apiKey, err := auth.GetAPIKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	data := url.Values{}
	data.Set("id", pptID)
	data.Set("cover_id", coverID)

	return h.makePostRequest(ctx, "/mcp/ppt/ppt-create-task", data, apiKey)
}

// uploadPPTToDStaff uploads a PPT file to DStaff platform after downloading
func (h *ToolHandler) uploadPPTToDStaff(ctx context.Context, pptID string, downloadResult *mcp.CallToolResult, dstaffAuth *auth.DStaffAuthContext) error {
	log.Printf("🔄 Processing PPT download result for DStaff upload (PPT ID: %s)", pptID)

	// Extract download URL from the result
	downloadURL, err := h.extractDownloadURL(downloadResult)
	if err != nil {
		return fmt.Errorf("failed to extract download URL: %w", err)
	}

	if downloadURL == "" {
		return fmt.Errorf("no download URL found in result")
	}

	log.Printf("📥 Downloading PPT from URL: %s", downloadURL)

	// Download the PPT file
	localFilePath, err := h.downloadFileToTemp(downloadURL, pptID+".pptx")
	if err != nil {
		return fmt.Errorf("failed to download PPT file: %w", err)
	}
	defer func() {
		// Clean up temporary file
		if err := os.Remove(localFilePath); err != nil {
			log.Printf("⚠️ Failed to clean up temporary file %s: %v", localFilePath, err)
		}
	}()

	log.Printf("📁 PPT downloaded to temporary file: %s", localFilePath)

	// Get file size before upload
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}
	fileSize := fileInfo.Size()

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, localFilePath, targetPath)
	if err != nil {
		return fmt.Errorf("failed to upload PPT to DStaff: %w", err)
	}

	log.Printf("📤 PPT uploaded successfully to DStaff path: %s (task_id: %s)", targetPath, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📊 File size: %d bytes", fileSize)

	return nil
}

// uploadPPTToDStaffWithSize uploads a PPT file to DStaff platform and returns the file size
func (h *ToolHandler) uploadPPTToDStaffWithSize(ctx context.Context, pptID string, downloadResult *mcp.CallToolResult, dstaffAuth *auth.DStaffAuthContext) (int64, error) {
	log.Printf("🔄 Processing PPT download result for DStaff upload (PPT ID: %s)", pptID)

	// Extract download URL from the result
	downloadURL, err := h.extractDownloadURL(downloadResult)
	if err != nil {
		return 0, fmt.Errorf("failed to extract download URL: %w", err)
	}

	if downloadURL == "" {
		return 0, fmt.Errorf("no download URL found in result")
	}

	log.Printf("📥 Downloading PPT from URL: %s", downloadURL)

	// Download the PPT file
	localFilePath, err := h.downloadFileToTemp(downloadURL, pptID+".pptx")
	if err != nil {
		return 0, fmt.Errorf("failed to download PPT file: %w", err)
	}
	defer func() {
		// Clean up temporary file
		if err := os.Remove(localFilePath); err != nil {
			log.Printf("⚠️ Failed to clean up temporary file %s: %v", localFilePath, err)
		}
	}()

	log.Printf("📁 PPT downloaded to temporary file: %s", localFilePath)

	// Get file size before upload
	fileInfo, err := os.Stat(localFilePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file info: %w", err)
	}
	fileSize := fileInfo.Size()

	// Upload to DStaff platform
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)

	uploadResponse, err := auth.UploadFileToDStaff(h.dstaffConfig, dstaffAuth, localFilePath, targetPath)
	if err != nil {
		return 0, fmt.Errorf("failed to upload PPT to DStaff: %w", err)
	}

	log.Printf("📤 PPT uploaded successfully to DStaff path: %s (task_id: %s)", targetPath, dstaffAuth.TaskID)
	log.Printf("✅ Upload response: %s", uploadResponse.Message)
	log.Printf("📊 File size: %d bytes", fileSize)

	return fileSize, nil
}

// createDStaffUploadResponseWithSize creates a proper MCP response for DStaff upload operations with file size
func (h *ToolHandler) createDStaffUploadResponseWithSize(originalResult *mcp.CallToolResult, pptID string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string, fileSize int64) *mcp.CallToolResult {
	targetPath := fmt.Sprintf("mcps_upload/ppt_downloads/%s.pptx", pptID)
	filename := fmt.Sprintf("%s.pptx", pptID)

	var response *auth.FileUploadResponse

	if success {
		// Create successful response using the same format as the reference
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "success",
			Message:     fmt.Sprintf("PPT文件上传成功！上传的文件路径：%s", targetPath),
			Attachments: []auth.Attachment{
				{
					Path:          targetPath,
					Filename:      filename,
					Type:          "file",
					ContentType:   "application/vnd.openxmlformats-officedocument.presentationml.presentation",
					ContentLength: fileSize,
				},
			},
		}
	} else {
		// Create error response
		response = &auth.FileUploadResponse{
			WorkType:    "mcp_tool",
			Compression: false,
			Status:      "error",
			Message:     fmt.Sprintf("PPT文件上传失败：%s", errorMsg),
			Attachments: []auth.Attachment{},
		}
	}

	// Convert to JSON
	responseJSON, _ := json.Marshal(response)

	// Return the JSON response
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{
				Type: "text",
				Text: string(responseJSON),
			},
		},
		IsError: !success,
	}
}

// createDStaffUploadResponse creates a proper MCP response for DStaff upload operations (legacy function)
func (h *ToolHandler) createDStaffUploadResponse(originalResult *mcp.CallToolResult, pptID string, dstaffAuth *auth.DStaffAuthContext, success bool, errorMsg string) *mcp.CallToolResult {
	return h.createDStaffUploadResponseWithSize(originalResult, pptID, dstaffAuth, success, errorMsg, 0)
}

// extractDownloadURL extracts the download URL from a CallToolResult
func (h *ToolHandler) extractDownloadURL(result *mcp.CallToolResult) (string, error) {
	if result == nil || len(result.Content) == 0 {
		return "", fmt.Errorf("empty result")
	}

	// Look for text content that contains JSON response
	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			// Try to parse as JSON to extract download URL
			var response map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &response); err != nil {
				// If not JSON, check if it's a direct URL
				text := strings.TrimSpace(textContent.Text)
				if strings.HasPrefix(text, "http://") || strings.HasPrefix(text, "https://") {
					return text, nil
				}
				continue
			}

			// Look for download URL in various possible locations in the JSON response
			downloadURL := h.findDownloadURLInResponse(response)
			if downloadURL != "" {
				return downloadURL, nil
			}
		}
	}

	return "", fmt.Errorf("no download URL found in result content")
}

// findDownloadURLInResponse searches for download URLs in the JSON response
func (h *ToolHandler) findDownloadURLInResponse(response map[string]interface{}) string {
	// Check common patterns for download URLs based on the ChatPPT API structure

	// Pattern 1: data.download_url (ChatPPT API format)
	if data, ok := response["data"].(map[string]interface{}); ok {
		if url, ok := data["download_url"].(string); ok && url != "" {
			return url
		}
	}

	// Pattern 2: data.downloads.video.url (for video downloads)
	if data, ok := response["data"].(map[string]interface{}); ok {
		if downloads, ok := data["downloads"].(map[string]interface{}); ok {
			if video, ok := downloads["video"].(map[string]interface{}); ok {
				if url, ok := video["url"].(string); ok {
					return url
				}
			}
		}
	}

	// Pattern 3: Direct URL field
	if url, ok := response["url"].(string); ok {
		return url
	}

	// Pattern 4: download_url field (root level)
	if url, ok := response["download_url"].(string); ok {
		return url
	}

	// Pattern 5: Look for any field containing "url" and starting with http
	for key, value := range response {
		if strings.Contains(strings.ToLower(key), "url") {
			if url, ok := value.(string); ok && (strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")) {
				return url
			}
		}
	}

	return ""
}

// downloadFileToTemp downloads a file from URL to a temporary location
func (h *ToolHandler) downloadFileToTemp(fileURL, filename string) (string, error) {
	// Create HTTP request
	req, err := http.NewRequest("GET", fileURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Send request
	resp, err := h.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("download failed with status %d", resp.StatusCode)
	}

	// Read file content
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Create temporary file
	tempDir := os.TempDir()
	timestamp := time.Now().Format("20060102_150405")
	localFilePath := filepath.Join(tempDir, fmt.Sprintf("ppt_%s_%s", timestamp, filename))

	// Save file
	if err := os.WriteFile(localFilePath, fileContent, 0644); err != nil {
		return "", fmt.Errorf("failed to save file: %w", err)
	}

	log.Printf("📁 File downloaded successfully: %s (%d bytes)", localFilePath, len(fileContent))
	return localFilePath, nil
}

// extractProjectIDFromResult extracts project_id (ppt_id) from API result
func (h *ToolHandler) extractProjectIDFromResult(result *mcp.CallToolResult) string {
	if result == nil || result.IsError || len(result.Content) == 0 {
		log.Printf("🔍 Debug: extractProjectIDFromResult - result is nil, error, or empty")
		return ""
	}

	// Try to extract from text content
	for i, content := range result.Content {
		log.Printf("🔍 Debug: extractProjectIDFromResult - processing content[%d]: %T", i, content)
		if textContent, ok := content.(mcp.TextContent); ok {
			log.Printf("🔍 Debug: extractProjectIDFromResult - text content: %s", textContent.Text)

			// Try to parse as JSON
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonData); err == nil {
				log.Printf("🔍 Debug: extractProjectIDFromResult - parsed JSON: %+v", jsonData)

				// Look for common project ID fields
				if pptID, ok := jsonData["ppt_id"].(string); ok && pptID != "" {
					log.Printf("✅ Found ppt_id: %s", pptID)
					return pptID
				}
				if id, ok := jsonData["id"].(string); ok && id != "" {
					log.Printf("✅ Found id: %s", id)
					return id
				}
				if projectID, ok := jsonData["project_id"].(string); ok && projectID != "" {
					log.Printf("✅ Found project_id: %s", projectID)
					return projectID
				}

				// Try to look in nested data structures
				if data, ok := jsonData["data"].(map[string]interface{}); ok {
					log.Printf("🔍 Debug: extractProjectIDFromResult - checking nested data: %+v", data)
					if pptID, ok := data["ppt_id"].(string); ok && pptID != "" {
						log.Printf("✅ Found ppt_id in data: %s", pptID)
						return pptID
					}
					if id, ok := data["id"].(string); ok && id != "" {
						log.Printf("✅ Found id in data: %s", id)
						return id
					}
					if projectID, ok := data["project_id"].(string); ok && projectID != "" {
						log.Printf("✅ Found project_id in data: %s", projectID)
						return projectID
					}
				}

				// Try to look for numeric IDs and convert to string
				if pptIDFloat, ok := jsonData["ppt_id"].(float64); ok {
					pptID := strconv.FormatFloat(pptIDFloat, 'f', 0, 64)
					log.Printf("✅ Found numeric ppt_id: %s", pptID)
					return pptID
				}
				if idFloat, ok := jsonData["id"].(float64); ok {
					id := strconv.FormatFloat(idFloat, 'f', 0, 64)
					log.Printf("✅ Found numeric id: %s", id)
					return id
				}

				log.Printf("⚠️ No recognized ID fields found in JSON")
			} else {
				log.Printf("⚠️ Failed to parse as JSON: %v", err)
			}
		}
	}

	log.Printf("❌ No project ID found in result")
	return ""
}

// resolveProjectID resolves project_id from task_id if not directly provided
func (h *ToolHandler) resolveProjectID(arguments map[string]interface{}) (string, error) {
	// First try to get ppt_id directly from arguments
	if pptID, ok := arguments["ppt_id"].(string); ok && pptID != "" {
		return pptID, nil
	}

	// If no direct ppt_id, try to resolve from task_id
	taskID := h.extractTaskIDFromRequest(arguments)
	if taskID == "" {
		return "", fmt.Errorf("neither ppt_id nor task_id provided")
	}

	if projectID, found := h.bindingManager.GetProjectID(taskID); found {
		log.Printf("🔗 Resolved project_id=%s from task_id=%s", projectID, taskID)
		return projectID, nil
	}

	return "", fmt.Errorf("no project_id found for task_id=%s", taskID)
}

// handleGeneratePPTComplete handles the generate_ppt_complete tool - complete PPT generation workflow
func (h *ToolHandler) handleGeneratePPTComplete(ctx context.Context, request mcp.CallToolRequest, arguments map[string]interface{}, dstaffAuth *auth.DStaffAuthContext) (*mcp.CallToolResult, error) {
	log.Printf("🚀 Executing 'generate_ppt_complete' tool - complete PPT generation workflow")

	// Extract task_id for progress reporting
	taskID := h.extractTaskIDFromRequest(arguments)

	// Create progress reporter
	reporter := progress.CreateProgressReporter(request, 3, h.progressSendFunc, h.logSendFunc, taskID)

	// Step 1: Start PPT generation
	if err := reporter.ReportStep(1, "开始PPT生成"); err != nil {
		log.Printf("⚠️ Failed to report step 1: %v", err)
	}

	// Determine generation type and call appropriate handler
	var buildResult *mcp.CallToolResult
	var err error

	if theme, ok := arguments["theme"].(string); ok && theme != "" {
		log.Printf("📝 Generating PPT from theme: %s", theme)
		buildResult, err = h.handleBuildPPT(ctx, arguments)
	} else if text, ok := arguments["text"].(string); ok && text != "" {
		log.Printf("📄 Generating PPT from text (length: %d)", len(text))
		buildResult, err = h.handleTextBuildPPT(ctx, arguments)
	} else if fileURL, ok := arguments["file_url"].(string); ok && fileURL != "" {
		log.Printf("📁 Generating PPT from file: %s", fileURL)
		if isThesis, ok := arguments["is_thesis"].(bool); ok && isThesis {
			buildResult, err = h.handleBuildThesisPPT(ctx, arguments)
		} else {
			buildResult, err = h.handleBuildPPTByFile(ctx, arguments)
		}
	} else {
		err = fmt.Errorf("no valid generation parameters provided (theme, text, or file_url required)")
	}

	if err != nil {
		reporter.Error(fmt.Sprintf("PPT生成失败: %v", err))
		return nil, err
	}

	// Extract PPT ID from build result
	log.Printf("🔍 Debug: Build result content: %+v", buildResult)
	if buildResult != nil && len(buildResult.Content) > 0 {
		for i, content := range buildResult.Content {
			log.Printf("🔍 Debug: Content[%d]: %+v", i, content)
			if textContent, ok := content.(mcp.TextContent); ok {
				log.Printf("🔍 Debug: Text content: %s", textContent.Text)

				// Check for API error response
				var jsonData map[string]interface{}
				if err := json.Unmarshal([]byte(textContent.Text), &jsonData); err == nil {
					if code, ok := jsonData["code"].(float64); ok && code != 200 {
						if msg, ok := jsonData["msg"].(string); ok {
							err = fmt.Errorf("API错误 (代码:%v): %s", code, msg)
							reporter.Error(err.Error())
							return nil, err
						}
					}
				}
			}
		}
	}

	pptID := h.extractProjectIDFromResult(buildResult)
	if pptID == "" {
		err = fmt.Errorf("failed to extract PPT ID from build result")
		reporter.Error(err.Error())
		return nil, err
	}

	log.Printf("✅ PPT generation started successfully, PPT ID: %s", pptID)

	// Bind task_id to project_id for future reference
	if taskID != "" {
		h.bindingManager.BindTaskToProject(taskID, pptID)
		log.Printf("🔗 Bound task_id=%s to project_id=%s", taskID, pptID)
	}

	// Step 2: Monitor generation status
	if err := reporter.ReportStep(2, "监测PPT生成状态"); err != nil {
		log.Printf("⚠️ Failed to report step 2: %v", err)
	}

	// Poll for completion
	maxAttempts := 600 // 50 minutes with 5-second intervals
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		log.Printf("🔍 Checking PPT generation status...")

		// Report progress for status checking
		//reporter.ReportStep(2, "检查PPT生成状态...")

		queryArgs := map[string]interface{}{
			"ppt_id": pptID,
		}
		if taskID != "" {
			queryArgs["Context"] = map[string]interface{}{
				"task_id": taskID,
			}
		}

		queryResult, err := h.handleQueryPPT(ctx, queryArgs)
		if err != nil {
			log.Printf("⚠️ Failed to query PPT status: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// Parse query result to check status
		status, processURL := h.parseQueryResult(queryResult)
		progressInfo := h.extractProgressInfo(queryResult)

		log.Printf("📊 PPT status: %d, process_url: %s", status, processURL)
		if progressInfo != "" {
			log.Printf("📈 Progress: %s", progressInfo)
		}

		if status == 2 { // Success
			log.Printf("✅ PPT generation completed successfully")
			if progressInfo != "" {
				log.Printf("📈 Final Progress: %s", progressInfo)
				reporter.ReportStep(2, fmt.Sprintf("PPT生成完成 - %s", progressInfo))
			} else {
				reporter.ReportStep(2, "PPT生成完成")
			}
			break
		} else if status == 3 { // Failed
			err = fmt.Errorf("PPT generation failed")
			reporter.Error(err.Error())
			return nil, err
		} else if status == 1 { // Still generating
			if progressInfo != "" {
				log.Printf("⏳ PPT still generating: %s", progressInfo)
				// Report detailed progress to client
				reporter.ReportStep(2, fmt.Sprintf("PPT生成中 - %s", progressInfo))
			} else {
				log.Printf("⏳ PPT still generating, waiting...")
				reporter.ReportStep(2, "PPT生成中 - 等待状态更新...")
			}
			time.Sleep(5 * time.Second)
			continue
		} else {
			log.Printf("⚠️ Unknown status: %d, continuing to wait...", status)
			if progressInfo != "" {
				reporter.ReportStep(2, fmt.Sprintf("PPT生成状态未知 (状态:%d) - %s", status, progressInfo))
			} else {
				reporter.ReportStep(2, fmt.Sprintf("PPT生成状态未知 (状态:%d) - 继续等待...", status))
			}
			time.Sleep(5 * time.Second)
			continue
		}
	}

	// Step 3: Download PPT
	if err := reporter.ReportStep(3, "下载PPT文件"); err != nil {
		log.Printf("⚠️ Failed to report step 3: %v", err)
	}

	downloadArgs := map[string]interface{}{
		"ppt_id": pptID,
	}
	if taskID != "" {
		downloadArgs["Context"] = map[string]interface{}{
			"task_id": taskID,
		}
	}

	downloadResult, err := h.handleDownloadPPTWithDStaff(ctx, downloadArgs, dstaffAuth)
	if err != nil {
		reporter.Error(fmt.Sprintf("PPT下载失败: %v", err))
		return nil, err
	}

	log.Printf("✅ PPT downloaded successfully")

	// Complete the workflow immediately after download
	if err := reporter.Complete("PPT生成完成，文件已准备就绪"); err != nil {
		log.Printf("⚠️ Failed to report completion: %v", err)
	}

	log.Printf("🎉 Complete PPT generation workflow finished successfully")

	// Return the download result which includes all necessary information
	return downloadResult, nil
}

// parseQueryResult parses the query result to extract status and process_url
func (h *ToolHandler) parseQueryResult(result *mcp.CallToolResult) (status int, processURL string) {
	if result == nil || result.IsError || len(result.Content) == 0 {
		log.Printf("🔍 Debug: parseQueryResult - result is nil, error, or empty")
		return 0, ""
	}

	// Try to extract from text content
	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			log.Printf("🔍 Debug: parseQueryResult - parsing text: %s", textContent.Text)

			// Try to parse as JSON
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonData); err == nil {
				log.Printf("🔍 Debug: parseQueryResult - parsed JSON: %+v", jsonData)

				// Look for status in nested data structure
				if data, ok := jsonData["data"].(map[string]interface{}); ok {
					log.Printf("🔍 Debug: parseQueryResult - checking nested data: %+v", data)

					// Extract status from data
					if statusFloat, ok := data["status"].(float64); ok {
						status = int(statusFloat)
						log.Printf("🔍 Debug: parseQueryResult - found status (float): %d", status)
					} else if statusInt, ok := data["status"].(int); ok {
						status = statusInt
						log.Printf("🔍 Debug: parseQueryResult - found status (int): %d", status)
					}

					// Extract process_url from data
					if url, ok := data["process_url"].(string); ok {
						processURL = url
						log.Printf("🔍 Debug: parseQueryResult - found process_url: %s", processURL)
					}
				} else {
					// Fallback: try to extract from top level
					if statusFloat, ok := jsonData["status"].(float64); ok {
						status = int(statusFloat)
						log.Printf("🔍 Debug: parseQueryResult - found top-level status (float): %d", status)
					} else if statusInt, ok := jsonData["status"].(int); ok {
						status = statusInt
						log.Printf("🔍 Debug: parseQueryResult - found top-level status (int): %d", status)
					}

					if url, ok := jsonData["process_url"].(string); ok {
						processURL = url
						log.Printf("🔍 Debug: parseQueryResult - found top-level process_url: %s", processURL)
					}
				}

				log.Printf("🔍 Debug: parseQueryResult - final result: status=%d, processURL=%s", status, processURL)
				return status, processURL
			} else {
				log.Printf("⚠️ parseQueryResult - failed to parse JSON: %v", err)
			}
		}
	}

	log.Printf("❌ parseQueryResult - no valid content found")
	return 0, ""
}

// extractProgressInfo extracts detailed progress information from query result
func (h *ToolHandler) extractProgressInfo(result *mcp.CallToolResult) string {
	if result == nil || result.IsError || len(result.Content) == 0 {
		return ""
	}

	for _, content := range result.Content {
		if textContent, ok := content.(mcp.TextContent); ok {
			var jsonData map[string]interface{}
			if err := json.Unmarshal([]byte(textContent.Text), &jsonData); err == nil {
				if data, ok := jsonData["data"].(map[string]interface{}); ok {
					var progressParts []string

					// Extract state description
					if stateDesc, ok := data["state_description"].(string); ok && stateDesc != "" {
						progressParts = append(progressParts, stateDesc)
					}

					// Extract progress percentage
					if progress, ok := data["progress"].(float64); ok && progress > 0 {
						progressParts = append(progressParts, fmt.Sprintf("%.0f%%", progress))
					}

					// Extract page count
					if pageCount, ok := data["page_count"].(float64); ok && pageCount > 0 {
						progressParts = append(progressParts, fmt.Sprintf("共%.0f页", pageCount))
					}

					// Extract title
					if title, ok := data["ppt_title"].(string); ok && title != "" {
						progressParts = append(progressParts, fmt.Sprintf("标题: %s", title))
					}

					if len(progressParts) > 0 {
						return strings.Join(progressParts, " | ")
					}
				}
			}
		}
	}

	return ""
}
