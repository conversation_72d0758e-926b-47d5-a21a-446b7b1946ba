package config

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
)

// Config holds all configuration for the ChatPPT MCP server
type Config struct {
	// API Configuration
	APIPPTKey string
	APIBase   string

	// Server Configuration
	Port      int
	Ports     []int // Multiple ports support
	LogLevel  string
	Transport string
	BaseURL   string

	// Authentication
	SSEAccessKey string

	// DStaff Integration
	DStaff *DStaffConfig
}

// DStaffConfig holds dstaff platform configuration
type DStaffConfig struct {
	Enabled         bool
	EndpointURL     string
	AuthServiceURL  string
	FileUploadURL   string
	FileDownloadURL string
	UseOfficialAuth bool
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	config := &Config{
		// Default values
		APIBase:   getEnvOrDefault("API_BASE", "https://saas.api.yoo-ai.com"),
		Port:      getEnvAsIntOrDefault("MCP_PORT", 8000),
		LogLevel:  getEnvOrDefault("LOG_LEVEL", "INFO"),
		Transport: getEnvOrDefault("MCP_TRANSPORT", "stdio"),
		BaseURL:   getEnvOrDefault("MCP_BASE_URL", ""),
	}

	// Parse multiple ports if specified
	if portsStr := os.Getenv("MCP_PORTS"); portsStr != "" {
		ports, err := parsePortsList(portsStr)
		if err != nil {
			return nil, fmt.Errorf("invalid MCP_PORTS format: %w", err)
		}
		config.Ports = ports
		if len(ports) > 0 {
			config.Port = ports[0] // Set primary port
		}
	}

	// Required configuration
	config.APIPPTKey = os.Getenv("API_PPT_KEY")
	if config.APIPPTKey == "" {
		return nil, fmt.Errorf("API_PPT_KEY environment variable is required")
	}

	// Optional configuration
	config.SSEAccessKey = os.Getenv("SSE_ACCESS_KEY")

	// Load DStaff configuration
	config.DStaff = loadDStaffConfig()

	// Validate transport mode
	validTransports := []string{"stdio", "sse", "streamable_http", "mixed"}
	if !contains(validTransports, config.Transport) {
		return nil, fmt.Errorf("invalid transport mode: %s. Valid options: %s",
			config.Transport, strings.Join(validTransports, ", "))
	}

	// Validate log level
	validLogLevels := []string{"DEBUG", "INFO", "WARNING", "ERROR"}
	config.LogLevel = strings.ToUpper(config.LogLevel)
	if !contains(validLogLevels, config.LogLevel) {
		return nil, fmt.Errorf("invalid log level: %s. Valid options: %s",
			config.LogLevel, strings.Join(validLogLevels, ", "))
	}

	return config, nil
}

// LogConfig logs the current configuration (without sensitive data)
func (c *Config) LogConfig() {
	log.Println("=" + strings.Repeat("=", 59))
	log.Println("🚀 ChatPPT MCP Server Configuration")
	log.Println("=" + strings.Repeat("=", 59))
	log.Printf("📊 Log level: %s", c.LogLevel)
	log.Printf("🌐 API Base URL: %s", c.APIBase)
	log.Printf("🔑 API Key configured: %s", boolToYesNo(c.APIPPTKey != ""))
	log.Printf("🔐 SSE Access Key configured: %s", boolToYesNo(c.SSEAccessKey != ""))
	log.Printf("🚀 Transport mode: %s", c.Transport)
	if len(c.Ports) > 1 {
		log.Printf("🌐 Server ports: %v", c.Ports)
	} else {
		log.Printf("🌐 Server port: %d", c.Port)
	}

	// Log DStaff configuration
	if c.DStaff.Enabled {
		log.Printf("🔗 DStaff integration: %s", boolToYesNo(c.DStaff.Enabled))
		log.Printf("🌐 DStaff endpoint: %s", c.DStaff.EndpointURL)
		log.Printf("🔐 DStaff official auth: %s", boolToYesNo(c.DStaff.UseOfficialAuth))
	} else {
		log.Printf("🔗 DStaff integration: %s", boolToYesNo(c.DStaff.Enabled))
	}

	log.Println("=" + strings.Repeat("=", 59))
}

// Helper functions
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsIntOrDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func boolToYesNo(b bool) string {
	if b {
		return "✅ Yes"
	}
	return "❌ No"
}

// parsePortsList parses a comma-separated list of ports
func parsePortsList(portsStr string) ([]int, error) {
	parts := strings.Split(portsStr, ",")
	ports := make([]int, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		port, err := strconv.Atoi(part)
		if err != nil {
			return nil, fmt.Errorf("invalid port '%s': %w", part, err)
		}

		if port < 1 || port > 65535 {
			return nil, fmt.Errorf("port %d is out of valid range (1-65535)", port)
		}

		ports = append(ports, port)
	}

	return ports, nil
}

// loadDStaffConfig loads dstaff configuration from environment variables
func loadDStaffConfig() *DStaffConfig {
	enabled := os.Getenv("DSTAFF_ENABLED") == "true"
	if !enabled {
		return &DStaffConfig{Enabled: false}
	}

	endpointURL := os.Getenv("DSTAFF_ENDPOINT_URL")
	if endpointURL == "" {
		endpointURL = "http://10.50.5.3:8800" // Default endpoint
	}

	return &DStaffConfig{
		Enabled:         true,
		EndpointURL:     endpointURL,
		AuthServiceURL:  endpointURL + "/api/v1/mcp/validateToken",
		FileUploadURL:   endpointURL + "/api/v1/mcp/file/upload",
		FileDownloadURL: endpointURL + "/api/v1/mcp/file/download",
		UseOfficialAuth: os.Getenv("DSTAFF_USE_OFFICIAL_AUTH") == "true",
	}
}
