package transport

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/mark3labs/mcp-go/mcp"
	"io"
	"log"
	"net/http"
	"strings"
	"sync"

	"chatppt-mcp-go/internal/auth"
	"chatppt-mcp-go/internal/binding"
	"chatppt-mcp-go/internal/config"
	"chatppt-mcp-go/internal/tools"
	"github.com/mark3labs/mcp-go/server"
)

// MCPServerWrapper wraps the MCP server with our custom functionality
type MCPServerWrapper struct {
	server      *server.MCPServer
	toolHandler *tools.ToolHandler
	config      *config.Config
}

// NewMCPServerWrapper creates a new MCP server wrapper
func NewMCPServerWrapper(cfg *config.Config) *MCPServerWrapper {
	// Create MCP server
	mcpServer := server.NewMCPServer(
		"ChatPPT MCP Server",
		"1.0.0",
		server.WithToolCapabilities(true),
		server.WithLogging(),
	)

	// Create task binding manager
	bindingManager, err := binding.NewTaskBindingManager("./data")
	if err != nil {
		log.Fatalf("❌ Failed to create task binding manager: %v", err)
	}

	// Create tool handler with binding manager
	toolHandler := tools.NewToolHandler(cfg.APIBase, cfg.DStaff, bindingManager)

	// Create wrapper first
	wrapper := &MCPServerWrapper{
		server:      mcpServer,
		toolHandler: toolHandler,
		config:      cfg,
	}

	// Add all tools with individual handlers
	allTools := tools.GetAllTools()
	for _, tool := range allTools {
		toolName := tool.Name
		mcpServer.AddTool(tool, wrapper.createToolHandler(toolName))
	}

	return wrapper
}

// ServeStdio starts the server in stdio mode
func (w *MCPServerWrapper) ServeStdio() error {
	log.Println("🚀 Starting ChatPPT MCP Server in stdio mode...")

	contextFunc := auth.StdioContextFunc(w.config.APIPPTKey, w.config.SSEAccessKey)
	return server.ServeStdio(w.server, server.WithStdioContextFunc(contextFunc))
}

// ServeSSE starts the server in SSE mode
func (w *MCPServerWrapper) ServeSSE() error {
	if len(w.config.Ports) > 1 {
		return w.serveSSEMultiPort()
	}
	return w.serveSSESinglePort()
}

// ServeStreamableHTTP starts the server in Streamable HTTP mode
func (w *MCPServerWrapper) ServeStreamableHTTP() error {
	return w.serveStreamableHTTPSinglePort()
}

// ServeMixed starts both SSE and Streamable HTTP servers
func (w *MCPServerWrapper) ServeMixed() error {
	if len(w.config.Ports) < 2 {
		return fmt.Errorf("mixed mode requires at least 2 ports")
	}
	return w.serveSSEMultiPort()
}

// serveSSESinglePort starts SSE server on a single port
func (w *MCPServerWrapper) serveSSESinglePort() error {
	log.Printf("🚀 Starting ChatPPT MCP Server in SSE mode on port %d...", w.config.Port)

	// Use configured BaseURL or default to localhost with internal port
	baseURL := w.config.BaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("http://localhost:%d", w.config.Port)
	}

	// Create authenticated SSE server with DStaff support
	sseServer := w.createAuthenticatedSSEServerWithDStaff(fmt.Sprintf("%d", w.config.Port))

	addr := fmt.Sprintf(":%d", w.config.Port)
	log.Printf("🌐 SSE server will be available at %s", baseURL)
	log.Printf("🌐 SSE endpoint: %s/sse", baseURL)
	log.Printf("🌐 Messages endpoint: %s/message", baseURL)
	log.Printf("🌐 Health endpoint: %s/health", baseURL)

	return sseServer.Start(addr)
}

// serveStreamableHTTPSinglePort starts Streamable HTTP server on a single port
func (w *MCPServerWrapper) serveStreamableHTTPSinglePort() error {
	log.Printf("🚀 Starting ChatPPT MCP Server in Streamable HTTP mode on port %d...", w.config.Port)

	// Note: The mcp-go library doesn't have a complete StreamableHTTPServer implementation yet
	// For now, we'll use SSE as a fallback which provides similar functionality
	log.Println("⚠️ Streamable HTTP mode not yet fully implemented in mcp-go, using SSE mode as fallback")

	return w.serveSSESinglePort()
}

// serveSSEMultiPort starts SSE and Streamable HTTP servers on multiple ports
func (w *MCPServerWrapper) serveSSEMultiPort() error {
	if len(w.config.Ports) < 2 {
		return fmt.Errorf("need at least 2 ports for SSE and Streamable HTTP servers")
	}

	log.Printf("🚀 Starting ChatPPT MCP Server with SSE and Streamable HTTP on ports %v...", w.config.Ports)

	var wg sync.WaitGroup
	errChan := make(chan error, 2)

	// Start SSE server on first port
	wg.Add(1)
	go func() {
		defer wg.Done()

		port := w.config.Ports[0]
		// Use configured BaseURL or default to localhost with internal port
		baseURL := w.config.BaseURL
		if baseURL == "" {
			baseURL = fmt.Sprintf("http://localhost:%d", port)
		}

		sseServer := w.createAuthenticatedSSEServerWithDStaff(fmt.Sprintf("%d", port))

		addr := fmt.Sprintf(":%d", port)
		log.Printf("🌐 SSE server will be available at %s", baseURL)
		log.Printf("🌐 SSE endpoint: %s/sse", baseURL)
		log.Printf("🌐 Messages endpoint: %s/message", baseURL)
		log.Printf("🌐 Health endpoint: %s/health", baseURL)

		if err := sseServer.Start(addr); err != nil {
			errChan <- fmt.Errorf("failed to start SSE server on port %d: %w", port, err)
		}
	}()

	// Start Streamable HTTP server on second port
	wg.Add(1)
	go func() {
		defer wg.Done()

		port := w.config.Ports[1]
		// Use configured BaseURL or default to localhost with internal port
		// For second port, we need to adjust the port in the URL
		baseURL := w.config.BaseURL
		if baseURL == "" {
			baseURL = fmt.Sprintf("http://localhost:%d", port)
		} else {
			// If BaseURL is configured, we need to adjust it for the second port
			// This assumes the BaseURL pattern and increments the port
			baseURL = strings.Replace(baseURL, "48083", "48084", 1)
		}

		log.Printf("🌐 Starting Streamable HTTP server on port %d", port)

		// Create HTTP server with API routes and DStaff support
		httpServer := w.createAuthenticatedHTTPServerWithAPI(fmt.Sprintf("%d", port))

		log.Printf("🌐 Streamable HTTP server will be available at %s", baseURL)
		log.Printf("🌐 HTTP endpoint: %s/", baseURL)
		log.Printf("🌐 Health endpoint: %s/health", baseURL)

		if err := httpServer.ListenAndServe(); err != nil {
			errChan <- fmt.Errorf("failed to start Streamable HTTP server on port %d: %w", port, err)
		}
	}()

	// Wait for the first error or all servers to start
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// Return the first error if any
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}

// GetServer returns the underlying MCP server
func (w *MCPServerWrapper) GetServer() *server.MCPServer {
	return w.server
}

// GetConfig returns the server configuration
func (w *MCPServerWrapper) GetConfig() *config.Config {
	return w.config
}

// healthCheckHandler handles health check requests
func (w *MCPServerWrapper) healthCheckHandler(rw http.ResponseWriter, r *http.Request) {
	rw.Header().Set("Content-Type", "application/json")
	rw.WriteHeader(http.StatusOK)
	rw.Write([]byte(`{"status":"healthy","service":"chatppt-mcp-server","version":"1.0.0"}`))
}

// createAuthenticatedSSEServerWithDStaff creates an SSE server with DStaff authentication support
func (w *MCPServerWrapper) createAuthenticatedSSEServerWithDStaff(port string) *server.SSEServer {
	baseURL := w.config.BaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("http://localhost:%s", port)
	}

	// Create authentication middleware with DStaff support
	authMiddleware := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(rw http.ResponseWriter, r *http.Request) {
			// Skip authentication for health check endpoint
			if r.URL.Path == "/health" {
				w.healthCheckHandler(rw, r)
				return
			}

			// Validate authentication (supports both key-based and DStaff)
			if !w.validateAuthentication(r) {
				log.Printf("❌ SSE connection rejected due to authentication failure from %s", r.RemoteAddr)
				http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
				return
			}

			// Add DStaff auth context if enabled
			ctx := r.Context()
			if w.config.DStaff.Enabled {
				log.Printf("🌐 === DStaff SSE Request Processing ===")
				log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
				log.Printf("🔍 Request Headers:")

				// Log relevant headers
				authHeader := r.Header.Get("Authorization")
				if authHeader != "" {
					log.Printf("   - Authorization: %s", w.maskAuthHeaderForLog(authHeader))
				} else {
					log.Printf("   - Authorization: Not provided")
				}

				taskIDHeader := r.Header.Get("X-Task-ID")
				if taskIDHeader != "" {
					log.Printf("   - X-Task-ID: %s", taskIDHeader)
				} else {
					log.Printf("   - X-Task-ID: Not provided")
				}

				// Log query parameters
				log.Printf("🔍 Query Parameters:")
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}

				// Extract token from Authorization header and add to context
				if strings.HasPrefix(authHeader, "Bearer ") {
					token := strings.TrimPrefix(authHeader, "Bearer ")
					ctx = context.WithValue(ctx, "authorization_token", token)
					log.Printf("🔐 Bearer token extracted and added to context")
				}

				// Extract task_id and add to context
				taskID := auth.ExtractTaskIDFromContext(ctx, r)
				if taskID != "" {
					ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
					log.Printf("📋 Task ID extracted and added to context: %s", taskID)
				} else {
					log.Printf("⚠️ No task ID found in request")
				}

				log.Printf("✅ DStaff SSE request processing completed")
			}

			// Update request with new context
			r = r.WithContext(ctx)

			// Authentication passed, continue to SSE server
			next.ServeHTTP(rw, r)
		})
	}

	// Create SSE server
	sseServer := server.NewSSEServer(w.server)

	// Create a custom HTTP server with authentication middleware
	customServer := &http.Server{
		Handler: authMiddleware(sseServer),
	}

	// Return SSE server with custom HTTP server
	return server.NewSSEServer(
		w.server,
		server.WithHTTPServer(customServer),
	)
}

// validateAuthentication validates both traditional and DStaff authentication (based on ref implementation)
func (w *MCPServerWrapper) validateAuthentication(r *http.Request) bool {
	// Check if DStaff official auth is enabled (highest priority)
	if w.config.DStaff.Enabled && w.config.DStaff.UseOfficialAuth {
		// Extract Bearer token from Authorization header
		authHeader := r.Header.Get("Authorization")
		log.Printf("DStaff Auth Check: %s %s from %s, Auth header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)

		if authHeader == "" {
			log.Printf("Access denied: No Authorization header for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		if !strings.HasPrefix(authHeader, "Bearer ") {
			log.Printf("Access denied: Invalid Authorization header format for %s %s from %s, header: '%s'", r.Method, r.URL.Path, r.RemoteAddr, authHeader)
			return false
		}

		token := strings.TrimPrefix(authHeader, "Bearer ")
		log.Printf("Extracted token for validation: '%s' (length: %d)", token, len(token))

		if !auth.ValidateTokenWithDStaff(w.config.DStaff, token) {
			log.Printf("Access denied: Invalid token '%s' for %s %s from %s", token, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted (dstaff auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// Legacy authentication logic - only for /sse endpoint
	if r.URL.Path == "/sse" && w.config.SSEAccessKey != "" {
		providedKey := r.URL.Query().Get("key")
		log.Printf("Legacy Auth Check: %s %s from %s, query key: '%s', expected key: '%s'", r.Method, r.URL.Path, r.RemoteAddr, providedKey, w.config.SSEAccessKey)

		if providedKey == "" {
			log.Printf("Access denied: No key provided for %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		if providedKey != w.config.SSEAccessKey {
			log.Printf("Access denied: Invalid key '%s' (expected: '%s') for %s %s from %s", providedKey, w.config.SSEAccessKey, r.Method, r.URL.Path, r.RemoteAddr)
			return false
		}

		log.Printf("Access granted (legacy auth): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
		return true
	}

	// No authentication required for other endpoints (like /health, /message, etc.)
	log.Printf("Access granted (no auth required): %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
	return true
}

// createAuthenticatedHTTPServerWithAPI creates an HTTP server with API routes (with DStaff support)
func (w *MCPServerWrapper) createAuthenticatedHTTPServerWithAPI(port string) *http.Server {
	// Create a new HTTP mux
	mux := http.NewServeMux()

	// Add MCP streamable HTTP handler with DStaff auth context support
	mux.HandleFunc("/mcp", func(rw http.ResponseWriter, r *http.Request) {
		// Add DStaff auth context if enabled
		ctx := r.Context()
		if w.config.DStaff.Enabled {
			log.Printf("🌐 === DStaff HTTP API Request Processing ===")
			log.Printf("📡 Request: %s %s from %s", r.Method, r.URL.Path, r.RemoteAddr)
			log.Printf("🔍 Request Headers:")

			// Log relevant headers
			authHeader := r.Header.Get("Authorization")
			if authHeader != "" {
				log.Printf("   - Authorization: %s", w.maskAuthHeaderForLog(authHeader))
			} else {
				log.Printf("   - Authorization: Not provided")
			}

			taskIDHeader := r.Header.Get("X-Task-ID")
			if taskIDHeader != "" {
				log.Printf("   - X-Task-ID: %s", taskIDHeader)
			} else {
				log.Printf("   - X-Task-ID: Not provided")
			}

			contentType := r.Header.Get("Content-Type")
			if contentType != "" {
				log.Printf("   - Content-Type: %s", contentType)
			}

			// Log query parameters
			log.Printf("🔍 Query Parameters:")
			if len(r.URL.Query()) > 0 {
				for key, values := range r.URL.Query() {
					log.Printf("   - %s: %v", key, values)
				}
			} else {
				log.Printf("   - No query parameters")
			}

			// Extract token from Authorization header and add to context
			if strings.HasPrefix(authHeader, "Bearer ") {
				token := strings.TrimPrefix(authHeader, "Bearer ")
				ctx = context.WithValue(ctx, "authorization_token", token)
				log.Printf("🔐 Bearer token extracted and added to context")
			}

			// Extract task_id and add to context
			taskID := auth.ExtractTaskIDFromContext(ctx, r)
			if taskID != "" {
				ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
				log.Printf("📋 Task ID extracted and added to context: %s", taskID)
			} else {
				log.Printf("⚠️ No task ID found in request")
			}

			log.Printf("✅ DStaff HTTP API request processing completed")
		}

		// Update request with new context
		r = r.WithContext(ctx)

		// Handle with streamable server
		streamableServer := server.NewStreamableHTTPServer(w.server)
		streamableServer.ServeHTTP(rw, r)
	})

	// Add health check
	mux.HandleFunc("/health", func(rw http.ResponseWriter, r *http.Request) {
		rw.Header().Set("Content-Type", "application/json; charset=utf-8")
		rw.WriteHeader(http.StatusOK)
		rw.Write([]byte(`{"status":"healthy","service":"chatppt-mcp","version":"1.0.0"}`))
	})

	// Add root redirect to docs
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/mcp", http.StatusFound)
			return
		}
		http.NotFound(w, r)
	})

	server := &http.Server{
		Addr:    ":" + port,
		Handler: mux,
	}

	log.Printf("🌐 HTTP API server will be available at http://localhost:%s", port)
	log.Printf("🔗 MCP endpoint: http://localhost:%s/mcp", port)

	return server
}

// authenticatedHTTPHandler handles HTTP requests with authentication
func (w *MCPServerWrapper) authenticatedHTTPHandler(rw http.ResponseWriter, r *http.Request) {
	// Validate authentication using the unified method
	if !w.validateAuthentication(r) {
		log.Printf("❌ Streamable HTTP connection rejected due to authentication failure from %s", r.RemoteAddr)
		http.Error(rw, `{"error": "Authentication required"}`, http.StatusUnauthorized)
		return
	}

	// Create context with authentication info
	ctx := r.Context()
	ctx = auth.HTTPContextFunc(w.config.APIPPTKey, w.config.SSEAccessKey)(ctx, r)

	// Add DStaff auth context if enabled
	if w.config.DStaff.Enabled {
		// Extract token from Authorization header and add to context
		authHeader := r.Header.Get("Authorization")
		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			ctx = context.WithValue(ctx, "authorization_token", token)
		}

		// Extract task_id and add to context
		taskID := auth.ExtractTaskIDFromContext(ctx, r)
		if taskID != "" {
			ctx = context.WithValue(ctx, "dstaff_task_id", taskID)
		}
	}

	r = r.WithContext(ctx)

	// Handle the request based on method and content type
	if r.Method == "POST" {
		w.handleMCPRequest(rw, r)
	} else {
		// For GET requests, return server info
		w.handleServerInfo(rw, r)
	}
}

// handleMCPRequest handles MCP tool requests using JSON-RPC protocol
func (w *MCPServerWrapper) handleMCPRequest(rw http.ResponseWriter, r *http.Request) {
	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Failed to read request body: %v", err)
		w.sendJSONRPCError(rw, nil, -32700, "Failed to read request body")
		return
	}

	// Log the request for debugging (similar to ref implementation)
	log.Printf("=== MCP JSON-RPC Request ===")
	var jsonData interface{}
	if err := json.Unmarshal(body, &jsonData); err == nil {
		prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
		log.Printf("Request Body:\n%s", string(prettyJSON))
	} else {
		log.Printf("Request Body (Raw): %s", string(body))
	}
	log.Printf("============================")

	// Process the request using the MCP server's HandleMessage method
	ctx := r.Context()
	response := w.server.HandleMessage(ctx, json.RawMessage(body))

	// Set response headers
	rw.Header().Set("Content-Type", "application/json")

	// Handle the response
	if response == nil {
		// No response needed (e.g., for notifications)
		rw.WriteHeader(http.StatusOK)
		return
	}

	// Marshal and send the response
	responseBytes, err := json.Marshal(response)
	if err != nil {
		log.Printf("Failed to marshal response: %v", err)
		w.sendJSONRPCError(rw, nil, -32603, "Internal error")
		return
	}

	// Log the response for debugging
	log.Printf("=== MCP JSON-RPC Response ===")
	var responseData interface{}
	if err := json.Unmarshal(responseBytes, &responseData); err == nil {
		prettyJSON, _ := json.MarshalIndent(responseData, "", "  ")
		log.Printf("Response Body:\n%s", string(prettyJSON))
	} else {
		log.Printf("Response Body (Raw): %s", string(responseBytes))
	}
	log.Printf("=============================")

	rw.WriteHeader(http.StatusOK)
	rw.Write(responseBytes)
}

// sendJSONRPCError sends a JSON-RPC error response
func (w *MCPServerWrapper) sendJSONRPCError(rw http.ResponseWriter, id interface{}, code int, message string) {
	errorResponse := map[string]interface{}{
		"jsonrpc": "2.0",
		"id":      id,
		"error": map[string]interface{}{
			"code":    code,
			"message": message,
		},
	}

	rw.Header().Set("Content-Type", "application/json")
	rw.WriteHeader(http.StatusOK) // JSON-RPC errors are still HTTP 200
	json.NewEncoder(rw).Encode(errorResponse)
}

// handleServerInfo returns server information
func (w *MCPServerWrapper) handleServerInfo(rw http.ResponseWriter, r *http.Request) {
	info := map[string]interface{}{
		"name":           "ChatPPT MCP Server",
		"version":        "1.0.0",
		"transport":      "streamable_http",
		"status":         "running",
		"dstaff_enabled": w.config.DStaff.Enabled,
	}

	rw.Header().Set("Content-Type", "application/json")
	rw.WriteHeader(http.StatusOK)
	json.NewEncoder(rw).Encode(info)
}

// httpRequestLogger logs all HTTP requests with detailed information
func (w *MCPServerWrapper) httpRequestLogger(next http.Handler) http.Handler {
	return http.HandlerFunc(func(rw http.ResponseWriter, r *http.Request) {
		// Log basic request info
		log.Printf("=== HTTP Request ===")
		log.Printf("Method: %s", r.Method)
		log.Printf("Path: %s", r.URL.Path)
		log.Printf("Full URL: %s", r.URL.String())
		log.Printf("Remote Addr: %s", r.RemoteAddr)

		// Log headers
		log.Printf("Headers:")
		for name, values := range r.Header {
			for _, value := range values {
				// Mask sensitive headers
				if strings.ToLower(name) == "authorization" && len(value) > 20 {
					log.Printf("  %s: %s...%s", name, value[:10], value[len(value)-10:])
				} else {
					log.Printf("  %s: %s", name, value)
				}
			}
		}

		// Read and log request body if present
		if r.Body != nil && r.ContentLength != 0 {
			bodyBytes, err := io.ReadAll(r.Body)
			if err != nil {
				log.Printf("Error reading request body: %v", err)
			} else {
				// Restore the body for the next handler
				r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// Log the body content
				contentType := r.Header.Get("Content-Type")
				if strings.Contains(contentType, "application/json") {
					// Pretty print JSON
					var jsonData interface{}
					if err := json.Unmarshal(bodyBytes, &jsonData); err == nil {
						prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
						log.Printf("Request Body (JSON):\n%s", string(prettyJSON))
					} else {
						log.Printf("Request Body (Raw): %s", string(bodyBytes))
					}
				} else if strings.Contains(contentType, "multipart/form-data") {
					log.Printf("Request Body: [multipart/form-data - %d bytes]", len(bodyBytes))
				} else {
					// Log other content types as raw text (truncated if too long)
					bodyStr := string(bodyBytes)
					if len(bodyStr) > 1000 {
						log.Printf("Request Body (truncated): %s...", bodyStr[:1000])
					} else {
						log.Printf("Request Body: %s", bodyStr)
					}
				}
			}
		} else {
			log.Printf("Request Body: [empty]")
		}
		log.Printf("===================")

		// Call the next handler
		next.ServeHTTP(rw, r)
	})
}

// addDStaffAuthContext adds DStaff authentication context to the request context
func (w *MCPServerWrapper) addDStaffAuthContext(ctx context.Context, request mcp.CallToolRequest) context.Context {
	log.Printf("🔍 === DStaff Auth Context Setup ===")
	log.Printf("🔧 DStaff Config Enabled: %v", w.config.DStaff.Enabled)
	log.Printf("🛠️ Tool Name: %s", request.Params.Name)

	if !w.config.DStaff.Enabled {
		log.Printf("⚠️ DStaff integration is disabled, skipping auth context setup")
		return ctx
	}

	// Log current context values
	log.Printf("🔍 Current Context Values:")
	if authToken := ctx.Value("authorization_token"); authToken != nil {
		if tokenStr, ok := authToken.(string); ok {
			log.Printf("   - Authorization Token: %s", w.maskTokenForLog(tokenStr))
		}
	} else {
		log.Printf("   - Authorization Token: Not found")
	}

	if taskID := ctx.Value("dstaff_task_id"); taskID != nil {
		if taskIDStr, ok := taskID.(string); ok {
			log.Printf("   - DStaff Task ID: %s", taskIDStr)
		}
	} else {
		log.Printf("   - DStaff Task ID: Not found")
	}

	// Log request parameters for debugging
	log.Printf("🔍 Request Parameters:")
	if args, ok := request.Params.Arguments.(map[string]interface{}); ok {
		for key, value := range args {
			if key == "task_id" {
				log.Printf("   - %s: %v", key, value)
			} else {
				// Truncate long values for readability
				valueStr := fmt.Sprintf("%v", value)
				if len(valueStr) > 100 {
					valueStr = valueStr[:100] + "..."
				}
				log.Printf("   - %s: %s", key, valueStr)
			}
		}
	}

	log.Printf("✅ DStaff auth context setup completed")
	return ctx
}

// maskAuthHeaderForLog masks the authorization header for logging
func (w *MCPServerWrapper) maskAuthHeaderForLog(authHeader string) string {
	if len(authHeader) <= 20 {
		return strings.Repeat("*", len(authHeader))
	}
	return authHeader[:10] + "..." + strings.Repeat("*", len(authHeader)-13) + authHeader[len(authHeader)-3:]
}

// maskTokenForLog masks the token for logging
func (w *MCPServerWrapper) maskTokenForLog(token string) string {
	if len(token) <= 20 {
		return strings.Repeat("*", len(token))
	}
	return token[:10] + "..." + strings.Repeat("*", len(token)-13) + token[len(token)-3:]
}

// extractTaskIDFromRequest extracts task_id from request arguments
func (w *MCPServerWrapper) extractTaskIDFromRequest(argsMap map[string]interface{}) string {
	// Try to extract from Context parameter
	if contextParam, ok := argsMap["Context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract from context parameter (lowercase)
	if contextParam, ok := argsMap["context"].(map[string]interface{}); ok {
		if taskID, ok := contextParam["task_id"].(string); ok {
			return taskID
		}
	}

	// Try to extract directly from arguments
	if taskID, ok := argsMap["task_id"].(string); ok {
		return taskID
	}

	return ""
}

// createToolHandler creates a handler function for a specific tool
func (w *MCPServerWrapper) createToolHandler(toolName string) func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	return func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		log.Printf("🔧 Executing tool: %s", toolName)

		// Add authentication info to context
		authInfo := &auth.AuthInfo{
			APIPPTKey:    w.config.APIPPTKey,
			SSEAccessKey: w.config.SSEAccessKey,
		}
		ctx = auth.WithAuthInfo(ctx, authInfo)
		log.Printf("🔐 Authentication info added to context for tool: %s", toolName)

		// Add DStaff auth context if available
		ctx = w.addDStaffAuthContext(ctx, request)

		// 获取当前客户端会话，在闭包外捕获
		currentSession := server.ClientSessionFromContext(ctx)

		// 设置当前请求的进度发送函数，使用捕获的会话
		w.toolHandler.SetProgressFunctions(func(notification *mcp.ProgressNotification) error {
			// 直接使用字段值
			total := notification.Params.Total
			message := notification.Params.Message

			log.Printf("📊 Sending progress notification: %.0f/%.0f - %s",
				notification.Params.Progress, total, message)

			// 如果有进度令牌，记录进度信息
			if notification.Params.ProgressToken != nil {
				if taskID, ok := notification.Params.ProgressToken.(string); ok && taskID != "" {
					log.Printf("🔍 Debug: Progress for taskID: %s, current: %.0f, total: %.0f, message: %s",
						taskID, notification.Params.Progress, total, message)
				}
			}

			// 使用捕获的客户端会话
			if currentSession == nil {
				log.Printf("⚠️ No client session available, using fallback logging")
				log.Printf("📊 Progress: %.0f/%.0f - %s",
					notification.Params.Progress, total, message)
				return nil
			}

			if !currentSession.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				currentSession.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.server.SendNotificationToClient(ctx, "notifications/progress", map[string]any{
				"progress":      notification.Params.Progress,
				"total":         notification.Params.Total,
				"progressToken": notification.Params.ProgressToken,
				"message":       notification.Params.Message,
			})

			if err != nil {
				log.Printf("❌ Failed to send progress notification: %v", err)
				// 使用日志作为后备
				log.Printf("📊 Progress (fallback): %.0f/%.0f - %s",
					notification.Params.Progress, total, message)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Progress notification sent to current client")
			return nil
		}, func(notification *mcp.LoggingMessageNotification) error {
			log.Printf("📝 Sending log notification: [%s] %s",
				string(notification.Params.Level),
				notification.Params.Data)

			// 使用捕获的客户端会话
			if currentSession == nil {
				log.Printf("⚠️ No client session available, using fallback logging")
				log.Printf("📝 Log [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil
			}

			if !currentSession.Initialized() {
				log.Printf("⚠️ Client session not initialized, forcing initialization...")
				currentSession.Initialize()
				log.Printf("✅ Client session initialized successfully")
			}

			// 发送到当前客户端
			err := w.server.SendNotificationToClient(ctx, "notifications/message", map[string]any{
				"level":  string(notification.Params.Level),
				"logger": notification.Params.Logger,
				"data":   notification.Params.Data,
			})

			if err != nil {
				log.Printf("❌ Failed to send log notification: %v", err)
				// 使用日志作为后备
				log.Printf("📝 Log (fallback) [%s]: %s",
					string(notification.Params.Level),
					notification.Params.Data)
				return nil // 不返回错误，使用后备方案
			}

			log.Printf("✅ Log notification sent to current client")
			return nil
		})

		// 调用原始的工具处理器
		result, err := w.toolHandler.HandleToolCall(ctx, request)

		if err != nil {
			log.Printf("❌ Tool execution failed: %v", err)
			return result, err
		}

		log.Printf("✅ Tool executed successfully: %s", toolName)
		return result, nil
	}
}
