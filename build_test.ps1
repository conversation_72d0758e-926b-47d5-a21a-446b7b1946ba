Write-Host "🚀 Building ChatPPT MCP Server with Task Binding Feature..." -ForegroundColor Green

Write-Host ""
Write-Host "📦 Step 1: Cleaning up..." -ForegroundColor Yellow
if (Test-Path "chatppt-mcp-server.exe") { Remove-Item "chatppt-mcp-server.exe" }
if (Test-Path "data") { Remove-Item "data" -Recurse -Force }

Write-Host ""
Write-Host "📦 Step 2: Running go mod tidy..." -ForegroundColor Yellow
$result = & go mod tidy 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ go mod tidy failed: $result" -ForegroundColor Red
    Read-Host "Press Enter to continue"
    exit 1
}
Write-Host "✅ go mod tidy completed" -ForegroundColor Green

Write-Host ""
Write-Host "📦 Step 3: Building the server..." -ForegroundColor Yellow
$result = & go build -o chatppt-mcp-server.exe ./cmd/server 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed: $result" -ForegroundColor Red
    Read-Host "Press Enter to continue"
    exit 1
}

Write-Host ""
Write-Host "✅ Build successful! chatppt-mcp-server.exe created." -ForegroundColor Green

Write-Host ""
Write-Host "📋 Testing basic functionality..." -ForegroundColor Yellow
Write-Host "Creating test data directory..."
New-Item -ItemType Directory -Path "data" -Force | Out-Null

Write-Host ""
Write-Host "🎉 Build and basic setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📖 Usage:" -ForegroundColor Cyan
Write-Host "  - Run: ./chatppt-mcp-server.exe" -ForegroundColor White
Write-Host "  - Or with transport: ./chatppt-mcp-server.exe -transport sse" -ForegroundColor White
Write-Host ""
Write-Host "📁 Files created:" -ForegroundColor Cyan
Write-Host "  - chatppt-mcp-server.exe (main executable)" -ForegroundColor White
Write-Host "  - data/ (directory for task bindings)" -ForegroundColor White
Write-Host ""
Write-Host "📚 See TASK_BINDING_FEATURE.md for detailed usage instructions." -ForegroundColor Cyan

Read-Host "Press Enter to continue"
