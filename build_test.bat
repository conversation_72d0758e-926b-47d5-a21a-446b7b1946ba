@echo off
echo 🚀 Building ChatPPT MCP Server with Task Binding Feature...

echo.
echo 📦 Step 1: Cleaning up...
if exist chatppt-mcp-server.exe del chatppt-mcp-server.exe
if exist data rmdir /s /q data

echo.
echo 📦 Step 2: Running go mod tidy...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ go mod tidy failed
    pause
    exit /b 1
)

echo.
echo 📦 Step 3: Building the server...
go build -o chatppt-mcp-server.exe ./cmd/server
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build successful! chatppt-mcp-server.exe created.

echo.
echo 📋 Testing basic functionality...
echo Creating test data directory...
mkdir data 2>nul

echo.
echo 🎉 Build and basic setup completed successfully!
echo.
echo 📖 Usage:
echo   - Run: chatppt-mcp-server.exe
echo   - Or with transport: chatppt-mcp-server.exe -transport sse
echo.
echo 📁 Files created:
echo   - chatppt-mcp-server.exe (main executable)
echo   - data/ (directory for task bindings)
echo.
echo 📚 See TASK_BINDING_FEATURE.md for detailed usage instructions.

pause
