version: '3.8'

services:
  chatppt-mcp-server:
    image: chatppt-mcp-server:1
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chatppt-mcp-server
    restart: unless-stopped
    ports:
      - "48083:48083"
      - "48084:48084"
    environment:
      # Required: Your ChatPPT API Key from www.yoo-ai.com
      - API_PPT_KEY=${API_PPT_KEY}

      # Optional: Log level (DEBUG, INFO, WARNING, ERROR)
      - LOG_LEVEL=${LOG_LEVEL:-INFO}

      # Optional: SSE Access Key for authentication
      - SSE_ACCESS_KEY=${SSE_ACCESS_KEY}

      # MCP Server Ports (comma-separated)
      - MCP_PORTS=48083,48084

      # Optional: API Base URL
      - API_BASE=${API_BASE:-https://saas.api.yoo-ai.com}

      # Transport mode (stdio, sse, streamable_http, mixed)
      - MCP_TRANSPORT=${MCP_TRANSPORT:-mixed}

      # Base URL for external access (Docker port mapping)
      - MCP_BASE_URL=http://localhost:48083

      # DStaff Integration Configuration
      - DSTAFF_ENABLED=${DSTAFF_ENABLED:-false}
      - DSTAFF_ENDPOINT_URL=${DSTAFF_ENDPOINT_URL:-http://*********:8800}
      - DSTAFF_USE_OFFICIAL_AUTH=${DSTAFF_USE_OFFICIAL_AUTH:-false}
      - DSTAFF_TOKEN=${DSTAFF_TOKEN}
      - DSTAFF_TASK_ID=${DSTAFF_TASK_ID}

      # Optional: Enable MCP debug mode for detailed logging
      - MCP_DEBUG=${MCP_DEBUG:-false}
    volumes:
      # Optional: Mount logs directory
      - ./logs:/app/logs
      # Mount data directory for task bindings persistence
      - ./data:/app/data
    networks:
      - chatppt-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:48083/health || curl -f http://localhost:48084/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "com.chatppt.service=mcp-server"
      - "com.chatppt.version=1.0.0"
      - "com.chatppt.ports=48083,48084"

networks:
  chatppt-network:
    driver: bridge
    name: chatppt-network

volumes:
  logs:
    driver: local
